$(document).ready(function() {
    // 检测是否为移动设备
    var isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                   window.innerWidth <= 1024;

    // 如果是移动设备，禁用放大镜功能
    if (isMobile) {
        $('.magnifier-lens, .magnifier-preview').hide();
        return;
    }

    // 放大镜功能
    var $mainImage = $('#mainImage');
    var $lens = $('.magnifier-lens');
    var $preview = $('.magnifier-preview');
    var $thumbnails = $('#show > div > ul > li'); // 更精确的选择器
    var $hint = $('.magnifier-hint');

    // 当前显示的图片信息
    var currentImageData = {
        normal: 'images/xiangqing_05.jpg',
        large: 'images/xiangqing_05_large.jpg'
    };

    // 放大镜配置
    var magnifierConfig = {
        lensSize: 150,
        zoomLevel: 2.5
    };

    // 初始化第一个缩略图为激活状态
    $thumbnails.first().addClass('active');

    // 缩略图点击事件
    $thumbnails.on('click', function() {
        var $this = $(this);

        // 移除其他缩略图的激活状态
        $thumbnails.removeClass('active').css('opacity', '1');
        // 添加当前缩略图的激活状态
        $this.addClass('active').css('opacity', '1');

        // 获取图片路径
        var normalImage = $this.data('image');
        var largeImage = $this.data('large');

        if (normalImage) {
            // 更新当前图片数据
            currentImageData.normal = normalImage;
            currentImageData.large = largeImage || normalImage;

            // 更新主图背景
            $mainImage.css('background-image', 'url(' + normalImage + ')');

            // 如果预览区域正在显示，更新预览图片
            if ($preview.is(':visible')) {
                updatePreviewImage();
            }
        } else {
            // 如果没有data-image属性，使用CSS背景图片
            var bgImage = $this.css('background-image');
            if (bgImage && bgImage !== 'none') {
                var imageUrl = bgImage.replace(/^url\(['"]?/, '').replace(/['"]?\)$/, '');
                currentImageData.normal = imageUrl;
                currentImageData.large = imageUrl.replace('.jpg', '_large.jpg');
                $mainImage.css('background-image', bgImage);

                if ($preview.is(':visible')) {
                    updatePreviewImage();
                }
            }
        }
    });

    // 主图鼠标进入事件
    $mainImage.on('mouseenter', function(e) {
        $lens.show();
        $preview.show();
        $hint.css('opacity', '0'); // 隐藏提示
        updatePreviewImage();

        // 立即更新镜头位置
        updateLensPosition(e);
    });

    // 主图鼠标离开事件
    $mainImage.on('mouseleave', function() {
        $lens.hide();
        $preview.hide();
        $hint.css('opacity', '1'); // 显示提示
    });

    // 主图鼠标移动事件
    $mainImage.on('mousemove', function(e) {
        if (!$lens.is(':visible')) return;
        updateLensPosition(e);
    });

    // 更新镜头位置的函数
    function updateLensPosition(e) {
        var offset = $mainImage.offset();
        var x = e.pageX - offset.left;
        var y = e.pageY - offset.top;

        // 镜头尺寸
        var lensWidth = magnifierConfig.lensSize;
        var lensHeight = magnifierConfig.lensSize;

        // 主图尺寸
        var imageWidth = $mainImage.outerWidth();
        var imageHeight = $mainImage.outerHeight();

        // 计算镜头位置（确保不超出边界）
        var lensX = Math.max(0, Math.min(x - lensWidth / 2, imageWidth - lensWidth));
        var lensY = Math.max(0, Math.min(y - lensHeight / 2, imageHeight - lensHeight));

        // 设置镜头位置
        $lens.css({
            left: lensX + 'px',
            top: lensY + 'px'
        });

        // 更新预览图片位置
        updatePreviewPosition(lensX, lensY, imageWidth, imageHeight);
    }

    // 更新预览图片
    function updatePreviewImage() {
        var $previewImg = $preview.find('img');
        if ($previewImg.length === 0) {
            $previewImg = $('<img>').appendTo($preview);

            // 图片加载完成后设置尺寸
            $previewImg.on('load', function() {
                var img = this;
                var previewWidth = $preview.width();
                var previewHeight = $preview.height();

                // 设置预览图片尺寸为预览区域的2.5倍，实现放大效果
                $(img).css({
                    width: previewWidth * magnifierConfig.zoomLevel + 'px',
                    height: previewHeight * magnifierConfig.zoomLevel + 'px'
                });
            });
        }
        $previewImg.attr('src', currentImageData.large);
    }

    // 更新预览图片位置
    function updatePreviewPosition(lensX, lensY, imageWidth, imageHeight) {
        var $previewImg = $preview.find('img');
        if ($previewImg.length === 0) return;

        // 镜头尺寸
        var lensWidth = magnifierConfig.lensSize;
        var lensHeight = magnifierConfig.lensSize;

        // 预览区域尺寸
        var previewWidth = $preview.width();
        var previewHeight = $preview.height();

        // 计算放大比例
        var scaleX = (previewWidth * magnifierConfig.zoomLevel) / imageWidth;
        var scaleY = (previewHeight * magnifierConfig.zoomLevel) / imageHeight;

        // 计算预览图片的偏移位置
        var offsetX = -(lensX * scaleX);
        var offsetY = -(lensY * scaleY);

        // 设置预览图片位置
        $previewImg.css({
            left: offsetX + 'px',
            top: offsetY + 'px'
        });
    }

    // 为现有的缩略图添加数据属性（如果没有的话）
    if (!$thumbnails.first().data('image')) {
        $thumbnails.eq(0).attr({
            'data-image': 'images/xiangqing_03.jpg',
            'data-large': 'images/xiangqing_03_large.jpg'
        });
        $thumbnails.eq(1).attr({
            'data-image': 'images/xiangqing_08.jpg',
            'data-large': 'images/xiangqing_08_large.jpg'
        });
        $thumbnails.eq(2).attr({
            'data-image': 'images/xiangqing_11.jpg',
            'data-large': 'images/xiangqing_11_large.jpg'
        });
        $thumbnails.eq(3).attr({
            'data-image': 'images/xiangqing_14.jpg',
            'data-large': 'images/xiangqing_14_large.jpg'
        });
    }

    // 设置默认主图数据
    currentImageData = {
        normal: 'images/xiangqing_05.jpg',
        large: 'images/xiangqing_05_large.jpg'
    };

    // 初始化镜头尺寸
    $lens.css({
        width: magnifierConfig.lensSize + 'px',
        height: magnifierConfig.lensSize + 'px'
    });

    // 添加缩略图悬停效果
    $thumbnails.on('mouseenter', function() {
        $(this).css('opacity', '0.8');
    }).on('mouseleave', function() {
        if (!$(this).hasClass('active')) {
            $(this).css('opacity', '1');
        }
    });

    // 标记magnifier.js已加载
    window.magnifierLoaded = true;
    console.log('Magnifier.js loaded successfully');
});
