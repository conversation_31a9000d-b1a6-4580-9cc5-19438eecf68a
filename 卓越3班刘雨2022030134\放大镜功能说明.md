# 图片放大镜功能说明

## 功能概述
本项目为商品详情页面添加了类似淘宝的图片放大镜功能，提供了更好的商品图片查看体验。

## 主要特性

### 1. 淘宝风格的放大镜效果
- **方形镜头**: 使用150x150像素的方形镜头，更符合现代电商网站的设计风格
- **右侧预览**: 放大预览区域显示在主图右侧，尺寸为450x450像素
- **2.5倍放大**: 提供2.5倍的放大效果，清晰展示商品细节

### 2. 交互体验优化
- **鼠标悬停激活**: 鼠标悬停在主图上时自动显示放大镜
- **实时跟随**: 镜头实时跟随鼠标移动
- **边界限制**: 镜头不会超出主图边界
- **流畅动画**: 添加了平滑的过渡效果

### 3. 缩略图切换
- **点击切换**: 点击缩略图可以切换主图
- **激活状态**: 当前选中的缩略图有红色边框和阴影效果
- **悬停效果**: 缩略图悬停时有轻微的缩放效果

### 4. 视觉反馈
- **提示文字**: 主图右下角显示"鼠标悬停查看放大效果"提示
- **预览标题**: 放大预览区域顶部显示"放大预览"标题
- **阴影效果**: 预览区域有轻微的阴影，增强层次感

## 技术实现

### HTML结构
```html
<div class="main-image-container">
    <ol id="mainImage" style="background-image: url('images/xiangqing_05.jpg')">
        <div class="magnifier-hint">鼠标悬停查看放大效果</div>
    </ol>
    <div class="magnifier-container">
        <div class="magnifier-lens"></div>
        <div class="magnifier-preview"></div>
    </div>
</div>
```

### CSS关键样式
- `.magnifier-lens`: 放大镜镜头样式
- `.magnifier-preview`: 预览区域样式
- `.main-image-container`: 主图容器，使用相对定位

### JavaScript功能
- 鼠标事件处理（mouseenter, mouseleave, mousemove）
- 镜头位置计算和边界检测
- 预览图片的动态加载和位置计算
- 缩略图切换逻辑

## 文件说明

### 修改的文件
1. **xiangqing.html**: 商品详情页面，已包含放大镜HTML结构
2. **css/xiangqing.css**: 样式文件，添加了放大镜相关样式
3. **js/magnifier.js**: JavaScript功能文件，实现放大镜交互逻辑

### 图片资源
项目中包含了对应的大图资源：
- `xiangqing_03_large.jpg`
- `xiangqing_05_large.jpg`
- `xiangqing_08_large.jpg`
- `xiangqing_11_large.jpg`
- `xiangqing_14_large.jpg`

## 使用方法

1. 打开 `xiangqing.html` 页面
2. 将鼠标悬停在主图上
3. 移动鼠标查看不同区域的放大效果
4. 点击左侧缩略图切换不同的商品图片

## 浏览器兼容性
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 快速测试

### 测试页面
项目中包含了一个专门的测试页面：`test-magnifier.html`
- 打开此页面可以查看功能说明和测试指南
- 提供了直接链接到商品详情页面进行测试

### 测试步骤
1. 在浏览器中打开 `xiangqing.html`
2. 将鼠标悬停在主图上
3. 观察右侧出现的放大预览区域
4. 移动鼠标查看不同区域的放大效果
5. 点击左侧缩略图测试图片切换功能

## 自定义配置

### 修改放大倍数
在 `js/magnifier.js` 中修改 `magnifierConfig.zoomLevel` 的值：
```javascript
var magnifierConfig = {
    lensSize: 150,        // 镜头尺寸
    zoomLevel: 2.5        // 放大倍数（可调整为 2.0, 3.0 等）
};
```

### 修改镜头尺寸
修改 `magnifierConfig.lensSize` 的值，同时需要在CSS中同步修改：
```css
.magnifier-lens {
    width: 150px;   /* 与 lensSize 保持一致 */
    height: 150px;
}
```

### 修改预览区域尺寸
在CSS中修改 `.magnifier-preview` 的宽高：
```css
.magnifier-preview {
    width: 450px;   /* 可调整为其他尺寸 */
    height: 450px;
}
```

## 注意事项
- 确保大图资源存在，否则放大功能可能无法正常工作
- 建议大图尺寸至少为主图的2-3倍，以获得最佳放大效果
- 在移动设备上，放大镜功能会自动禁用，避免影响触摸体验
- 如果需要支持更多图片格式，请在JavaScript中相应修改文件扩展名处理逻辑

## 故障排除

### 常见问题
1. **放大镜不显示**: 检查jQuery是否正确加载，确保magnifier.js文件路径正确
2. **预览图片不清晰**: 确保对应的_large.jpg文件存在且尺寸足够大
3. **缩略图切换无效**: 检查HTML中的data-image和data-large属性是否正确设置
4. **移动端显示异常**: 确保CSS媒体查询正确应用，检查viewport设置

### 调试方法
- 打开浏览器开发者工具查看控制台错误信息
- 检查网络面板确认图片资源加载状态
- 使用元素检查器查看CSS样式是否正确应用
