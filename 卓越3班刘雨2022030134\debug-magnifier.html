<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>放大镜功能调试页面</title>
    <script src="js/jquery-3.1.1.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .debug-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            margin: 5px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        #debugResults {
            font-family: monospace;
            font-size: 12px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔍 放大镜功能调试工具</h1>
        
        <div class="debug-section">
            <div class="debug-title">基础检查</div>
            <button class="test-button" onclick="runBasicChecks()">运行基础检查</button>
            <div id="basicChecks"></div>
        </div>

        <div class="debug-section">
            <div class="debug-title">DOM元素检查</div>
            <button class="test-button" onclick="checkDOMElements()">检查DOM元素</button>
            <div id="domChecks"></div>
        </div>

        <div class="debug-section">
            <div class="debug-title">图片资源检查</div>
            <button class="test-button" onclick="checkImageResources()">检查图片资源</button>
            <div id="imageChecks"></div>
        </div>

        <div class="debug-section">
            <div class="debug-title">功能测试</div>
            <button class="test-button" onclick="testMagnifierFunction()">测试放大镜功能</button>
            <div id="functionTests"></div>
        </div>

        <div class="debug-section">
            <div class="debug-title">快速修复</div>
            <button class="test-button" onclick="quickFix()">应用快速修复</button>
            <div id="quickFixResults"></div>
        </div>

        <div class="debug-section">
            <div class="debug-title">详细日志</div>
            <div id="debugResults"></div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const debugResults = document.getElementById('debugResults');
            const timestamp = new Date().toLocaleTimeString();
            debugResults.innerHTML += `[${timestamp}] ${type.toUpperCase()}: ${message}<br>`;
            debugResults.scrollTop = debugResults.scrollHeight;
        }

        function showStatus(containerId, message, type) {
            const container = document.getElementById(containerId);
            container.innerHTML += `<div class="status ${type}">${message}</div>`;
        }

        function runBasicChecks() {
            const container = document.getElementById('basicChecks');
            container.innerHTML = '';
            
            // 检查jQuery
            if (typeof $ !== 'undefined') {
                showStatus('basicChecks', '✓ jQuery已加载', 'success');
                log('jQuery检查通过');
            } else {
                showStatus('basicChecks', '✗ jQuery未加载', 'error');
                log('jQuery检查失败', 'error');
            }

            // 检查浏览器兼容性
            const userAgent = navigator.userAgent;
            if (userAgent.includes('Chrome') || userAgent.includes('Firefox') || userAgent.includes('Safari') || userAgent.includes('Edge')) {
                showStatus('basicChecks', '✓ 浏览器兼容', 'success');
                log('浏览器兼容性检查通过');
            } else {
                showStatus('basicChecks', '⚠ 浏览器可能不兼容', 'warning');
                log('浏览器兼容性检查警告', 'warning');
            }

            // 检查屏幕尺寸
            if (window.innerWidth >= 1024) {
                showStatus('basicChecks', '✓ 屏幕尺寸适合放大镜功能', 'success');
                log('屏幕尺寸检查通过');
            } else {
                showStatus('basicChecks', '⚠ 屏幕尺寸较小，放大镜可能被禁用', 'warning');
                log('屏幕尺寸检查警告', 'warning');
            }
        }

        function checkDOMElements() {
            const container = document.getElementById('domChecks');
            container.innerHTML = '';

            const elements = [
                { selector: '#mainImage', name: '主图容器' },
                { selector: '.magnifier-lens', name: '放大镜镜头' },
                { selector: '.magnifier-preview', name: '预览区域' },
                { selector: '#show > div > ul > li', name: '缩略图' },
                { selector: '.magnifier-hint', name: '提示文字' }
            ];

            elements.forEach(element => {
                try {
                    const el = document.querySelector(element.selector);
                    if (el) {
                        showStatus('domChecks', `✓ ${element.name}存在`, 'success');
                        log(`DOM元素检查通过: ${element.name}`);
                    } else {
                        showStatus('domChecks', `✗ ${element.name}不存在`, 'error');
                        log(`DOM元素检查失败: ${element.name}`, 'error');
                    }
                } catch (e) {
                    showStatus('domChecks', `✗ ${element.name}选择器错误`, 'error');
                    log(`DOM元素选择器错误: ${element.name} - ${e.message}`, 'error');
                }
            });
        }

        function checkImageResources() {
            const container = document.getElementById('imageChecks');
            container.innerHTML = '';

            const images = [
                'images/xiangqing_05.jpg',
                'images/xiangqing_05_large.jpg',
                'images/xiangqing_03.jpg',
                'images/xiangqing_03_large.jpg',
                'images/xiangqing_08.jpg',
                'images/xiangqing_08_large.jpg'
            ];

            let checkedCount = 0;
            images.forEach(imagePath => {
                const img = new Image();
                img.onload = function() {
                    showStatus('imageChecks', `✓ ${imagePath} 加载成功`, 'success');
                    log(`图片资源检查通过: ${imagePath}`);
                    checkedCount++;
                    if (checkedCount === images.length) {
                        showStatus('imageChecks', '✓ 所有图片资源检查完成', 'success');
                    }
                };
                img.onerror = function() {
                    showStatus('imageChecks', `✗ ${imagePath} 加载失败`, 'error');
                    log(`图片资源检查失败: ${imagePath}`, 'error');
                    checkedCount++;
                    if (checkedCount === images.length) {
                        showStatus('imageChecks', '⚠ 图片资源检查完成，部分失败', 'warning');
                    }
                };
                img.src = imagePath;
            });
        }

        function testMagnifierFunction() {
            const container = document.getElementById('functionTests');
            container.innerHTML = '';

            // 检查magnifier.js是否加载
            try {
                if (typeof window.magnifierLoaded === 'undefined') {
                    showStatus('functionTests', '⚠ magnifier.js可能未正确加载', 'warning');
                    log('magnifier.js加载状态未知', 'warning');
                }

                // 模拟鼠标事件测试
                const mainImage = document.getElementById('mainImage');
                if (mainImage) {
                    const event = new MouseEvent('mouseenter', {
                        view: window,
                        bubbles: true,
                        cancelable: true,
                        clientX: 100,
                        clientY: 100
                    });
                    mainImage.dispatchEvent(event);
                    
                    setTimeout(() => {
                        const lens = document.querySelector('.magnifier-lens');
                        const preview = document.querySelector('.magnifier-preview');
                        
                        if (lens && lens.style.display !== 'none') {
                            showStatus('functionTests', '✓ 放大镜镜头响应正常', 'success');
                            log('放大镜镜头功能测试通过');
                        } else {
                            showStatus('functionTests', '✗ 放大镜镜头未响应', 'error');
                            log('放大镜镜头功能测试失败', 'error');
                        }

                        if (preview && preview.style.display !== 'none') {
                            showStatus('functionTests', '✓ 预览区域响应正常', 'success');
                            log('预览区域功能测试通过');
                        } else {
                            showStatus('functionTests', '✗ 预览区域未响应', 'error');
                            log('预览区域功能测试失败', 'error');
                        }
                    }, 100);
                } else {
                    showStatus('functionTests', '✗ 主图元素不存在，无法测试', 'error');
                    log('主图元素不存在，功能测试失败', 'error');
                }
            } catch (e) {
                showStatus('functionTests', `✗ 功能测试出错: ${e.message}`, 'error');
                log(`功能测试异常: ${e.message}`, 'error');
            }
        }

        function quickFix() {
            const container = document.getElementById('quickFixResults');
            container.innerHTML = '';

            try {
                // 修复1: 确保CSS样式正确应用
                const lens = document.querySelector('.magnifier-lens');
                if (lens) {
                    lens.style.width = '150px';
                    lens.style.height = '150px';
                    lens.style.position = 'absolute';
                    lens.style.display = 'none';
                    showStatus('quickFixResults', '✓ 镜头样式已修复', 'success');
                    log('镜头样式快速修复完成');
                }

                // 修复2: 确保预览区域样式正确
                const preview = document.querySelector('.magnifier-preview');
                if (preview) {
                    preview.style.position = 'absolute';
                    preview.style.display = 'none';
                    preview.style.width = '450px';
                    preview.style.height = '450px';
                    showStatus('quickFixResults', '✓ 预览区域样式已修复', 'success');
                    log('预览区域样式快速修复完成');
                }

                // 修复3: 重新绑定事件（如果jQuery可用）
                if (typeof $ !== 'undefined') {
                    const $mainImage = $('#mainImage');
                    if ($mainImage.length > 0) {
                        // 移除旧事件
                        $mainImage.off('mouseenter mouseleave mousemove');
                        showStatus('quickFixResults', '✓ 事件绑定已重置', 'success');
                        log('事件绑定快速修复完成');
                    }
                }

                showStatus('quickFixResults', '✓ 快速修复完成，请重新测试功能', 'success');
                log('所有快速修复操作完成');

            } catch (e) {
                showStatus('quickFixResults', `✗ 快速修复失败: ${e.message}`, 'error');
                log(`快速修复异常: ${e.message}`, 'error');
            }
        }

        // 页面加载完成后自动运行基础检查
        document.addEventListener('DOMContentLoaded', function() {
            log('调试工具初始化完成');
            setTimeout(runBasicChecks, 500);
        });
    </script>
</body>
</html>
