@charset "utf-8";
/* CSS Document */
*{margin:0px;padding:0px;list-style:none;text-decoration:none;font-family:"微软雅黑", "宋体", "黑体";font-weight:normal;}
/*页头*/
#headCon{
	min-width:1200px;}
#headCon>div{
	width:1200px;
	margin:auto;
	margin-top:10px;
	overflow:hidden;
	}
#headCon>div>p{
	float:left;
	font-size:14px;
	color:#999;}
#headCon>div>ol{
	width:300px;
	float:right;
	overflow:hidden;}
#headCon>div>ol>li{
	overflow:hidden;
	float:left;}
#headCon>div>ol>li>a{
	font-size:14px;
	color:#999;
	float:left;
	margin-right:10px;
	}
#headCon>div>ol>li>a:hover{
	color:#fb0000;}
#headCon>div>ol>a{
	width:76px;
	height:14px;
	text-align:center;
	line-height:14px;
	float:left;
	font-size:14px;
	color:#999;
	border-left:1px solid #a1a1a1;
	border-right:1px solid #a1a1a1;
	margin-top:3px;}
#headCon>div>ol>a:hover{
	color:#fb0000;}
#headCon>div>ol>.shoppingCar{
	margin-left:10px;
	width:120px;
	border:none;
	height:20px;
	float:left;
	overflow:hidden;}
#headCon>div>ol>.shoppingCar>i{
	float:left;
	width:22px;
	height:18px;
	vertical-align:middle;
	background:url(../images/sprite.png) no-repeat 0px 0px;}
#headCon>div>ol>.shoppingCar>span{
	float:left;
	margin-left:10px;}
#headCon>ul{
	width:1200px;
	margin:auto;
	overflow:hidden;
	margin-top:30px;}
#headCon>ul>a{
	float:left;}
#headCon>ul>a>li{
	width:92px;
	height:63px;
	background:url(../images/sprite.png) no-repeat -22px 0px;}
#headCon>ul>ol{
	float:left;
	overflow:hidden;
	margin-left:180px;}
#headCon>ul>ol>li{
	float:left;
	margin-top:25px;
	margin-right:40px;}
#headCon>ul>ol>li>a{
	font-size:18px;
	color:#4c4c4c;}
#headCon>ul>ol>li>a:hover{
	color:#fb0000;}
#headCon>ul>ol>li>.first{
	color:#fb0000;}
#headCon>ul>ol>.search{
	margin-top:10px;
	margin-left:90px;}
#headCon>ul>ol>.search>input{
	width:254px;
	height:40px;
	border:1px solid #fb0000;
	border-radius:20px;
	outline:none;
	color:#999;
	padding-left:16px;
	font-size:16px;}
#headCon>ul>ol>.search>button{
	width:29px;
	height:29px;
	background:url(../images/sprite.png) no-repeat -115px 0px;
	outline:none;
	border:none;
	vertical-align:middle;
	margin-left:-60px;
	}
#headCon>ol{
	min-width:1200px;
	height:500px;
	margin:auto;
	background:url(../images/banner04_02.jpg) center no-repeat;
	margin-top:20px;}
/*内容*/
#contentCon{
	width:1200px;
	margin:auto;
	margin-top:80px;}
#contentCon>div{
	margin-left:20px;}
#contentCon>div>a{
	font-size:14px;
	color:#4c4c4c;}
#contentCon>div>a:hover{
	color:#fb0000;}
#contentCon>ul{
	overflow:hidden;
	margin-top:60px;}
#contentCon>ul>li{
	float:left;}
#contentCon>ul>li>a:hover{
	border:2px solid #fb0000;}
#contentCon>ul>.pic02{
	margin:0 8px;}
#contentCon>ul>.pic03{
	margin-right:8px;}
#contentCon>ul>li>.baxi{
	display:block;
	width:292px;
	height:400px;
	background:url(../images/bing01_03.jpg) center no-repeat;}
#contentCon>ul>li>.yili{
	display:block;
	width:292px;
	height:400px;
	background:url(../images/bing01_05.jpg) center no-repeat;}
#contentCon>ul>li>.mengniu{
	display:block;
	width:292px;
	height:400px;
	background:url(../images/bing01_07.jpg) center no-repeat;}
#contentCon>ul>li>.DQ{
	display:block;
	width:292px;
	height:400px;
	background:url(../images/bing01_09.jpg) center no-repeat;}
#contentCon>ul>li>.he{
	display:block;
	width:292px;
	height:400px;
	background:url(../images/bing02_03.jpg) center no-repeat;}
#contentCon>ul>li>.zhongjie{
	display:block;
	width:292px;
	height:400px;
	background:url(../images/bing02_05.jpg) center no-repeat;}
#contentCon>ul>li>.quechao{
	display:block;
	width:292px;
	height:400px;
	background:url(../images/bing02_07.jpg) center no-repeat;}
#contentCon>ul>li>.wuyang{
	display:block;
	width:292px;
	height:400px;
	background:url(../images/bing02_09.jpg) center no-repeat;}
#contentCon>ul>li>.le{
	display:block;
	width:292px;
	height:400px;
	background:url(../images/bing03_03.jpg) center no-repeat;}
#contentCon>ul>li>.mingzhi{
	display:block;
	width:292px;
	height:400px;
	background:url(../images/bing03_05.jpg) center no-repeat;}
#contentCon>ul>li>.city{
	display:block;
	width:292px;
	height:400px;
	background:url(../images/bing03_07.jpg) center no-repeat;}
#contentCon>ul>li>.haidai{
	display:block;
	width:292px;
	height:400px;
	background:url(../images/bing03_09.jpg) center no-repeat;}
#contentCon>ul>li>.caihong{
	display:block;
	width:292px;
	height:400px;
	background:url(../images/bing04_03.jpg) center no-repeat;}
#contentCon>ul>li>.beizi{
	display:block;
	width:292px;
	height:400px;
	background:url(../images/bing04_05.jpg) center no-repeat;}
#contentCon>ul>li>.sanjiao{
	display:block;
	width:292px;
	height:400px;
	background:url(../images/bing04_07.jpg) center no-repeat;}
#contentCon>ul>li>.Gelato{
	display:block;
	width:292px;
	height:400px;
	background:url(../images/bing04_09.jpg) center no-repeat;}
#contentCon>ul>li>div{
	overflow:hidden;
	margin-top:20px;
	margin-left:90px;}
#contentCon>ul>li>div>span{
	float:left;
	width:20px;
	height:45px;
	background:#fb0000;
	color:#fff;
	text-align:center;
	margin-top:3px;}
#contentCon>ul>li>div>.he{
	float:left;
	width:20px;
	height:65px;
	background:#fb0000;
	color:#fff;
	text-align:center;
	margin-top:3px;}
#contentCon>ul>li>div>ol{
	float:left;
	margin-left:10px;
	}
#contentCon>ul>li>div>ol>li{
	font-size:14px;
	color:#4c4c4c;}
#contentCon>ul>li>div>ol>p{
	font-size:14px;
	color:#fb0000;
	margin-top:13px;
	margin-left:28px;}
#contentCon>ol{
	width:1200px;
	height:50px;
	background:#eee;
	margin-top:60px;
	}
#contentCon>ol>a{
	float:left;
	font-size:14px;
	color:#4c4c4c;
	line-height:50px;
	margin-left:555px;}
#contentCon>ol>i{
	float:left;
	width:19px;
	height:25px;
	background:url(../images/sprite.png) no-repeat -668px 0px;
	margin-top:10px;
	margin-left:10px;}
/*页脚*/
#footCon>ul{
	width:1200px;
	height:168px;
	background:#eee;
	margin:auto;
	margin-top:100px;
	overflow:hidden;}
#footCon>ul>li{
	width:330px;
	float:left;
	margin-top:45px;
	margin-left:70px;
	overflow:hidden;}
#footCon>ul>li>.cold{
	width:65px;
	height:65px;
	background:url(../images/sprite.png) no-repeat -351px 0px;
	float:left;
	margin-top:10px;}
#footCon>ul>li>.wuxiu{
	width:69px;
	height:69px;
	background:url(../images/sprite.png) no-repeat -418px 0px;
	float:left;
	margin-top:10px;}
#footCon>ul>li>.baoyou{
	width:58px;
	height:72px;
	background:url(../images/sprite.png) no-repeat -488px 0px;
	float:left;
	margin-top:10px;}
#footCon>ul>li>span{
	float:left;
	width:1px;
	height:80px;
	background:#fb0000;
	margin:0 30px 0 30px;}
#footCon>ul>li>ol{
	float:left;}
#footCon>ul>li>ol>h2{
	font-size:30px;
	color:#fb0000;
	font-weight:normal;}
#footCon>ul>li>ol>p{
	font-size:24px;
	color:#4c4c4c;
	margin-top:10px;}
#footCon>ol{
	width:1200px;
	margin:auto;
	margin-top:50px;
	overflow:hidden;}
#footCon>ol>li{
	float:left;
	margin-left:120px;
	margin-right:85px;}
#footCon>ol>li>i{
	display:block;
	width:92px;
	height:62px;
	background:url(../images/sprite.png) no-repeat -23px 0px;
	margin-left:20px;}
#footCon>ol>li>a{
	display:block;
	font-size:14px;
	color:#4c4c4c;
	margin-top:20px;}
#footCon>ol>li>div{
	overflow:hidden;
	margin-top:24px;
	margin-left:10px;}
#footCon>ol>li>div>.weibo{
	float:left;
	width:33px;
	height:27px;
	background:url(../images/sprite.png) no-repeat -546px 0px;}
#footCon>ol>li>div>.weixin{
	float:left;
	width:36px;
	height:30px;
	background:url(../images/sprite.png) no-repeat -582px 0px;
	margin-left:40px;}
#footCon>ol>ul{
	width:538px;
	border-left:1px solid #b5b5b5;
	border-right:1px solid #b5b5b5;
	overflow:hidden;
	float:left;}
#footCon>ol>ul>li{
	float:left;
	margin-right:50px;}
#footCon>ol>ul>.text01{
	margin-left:80px;}
#footCon>ol>ul>li>p{
	font-size:14px;
	color:#4c4c4c;}
#footCon>ol>ul>li>span{
	display:block;
	width:50px;
	height:1px;
	background:#b5b5b5;
	margin-left:5px;
	margin-top:10px;}
#footCon>ol>ul>li>a{
	display:block;
	font-size:14px;
	color:#808080;
	margin-top:20px;}
#footCon>ol>div{
	text-align:center;
	float:left;
	margin-left:50px;}
#footCon>ol>div>p{
	font-size:14px;
	color:#4d4d4d;}
#footCon>ol>div>h2{
	font-size:18px;
	color:#4c4c4c;
	font-weight:normal;
	margin-top:20px;}
#footCon>ol>div>span{
	display:block;
	font-size:14px;
	color:#4d4d4d;
	margin-top:20px;}
#footCon>ol>div>a{
	display:block;
	width:75px;
	height:25px;
	border-radius:8px;
	background:#b5b5b5;
	color:#fff;
	margin-top:20px;
	margin-left:26px;
	font-size:14px;
	line-height:25px;}
#footCon>div{
	margin:auto;
	width:1200px;
	height:1px;
	background:#b5b5b5;
	margin-top:30px;}
#footCon>li{
	width:1200px;
	margin:auto;
	text-align:center;
	font-size:14px;
	margin-top:40px;
	color:#808080;}
#footCon>li>span{
	display:block;
	margin-top:20px;}

